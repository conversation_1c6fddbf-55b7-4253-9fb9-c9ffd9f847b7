# Documentação de Indicadores Técnicos em PyQuotex

## Índice
1. [Introdução](#introdução)
2. [Timeframes Disponíveis](#timeframes-disponíveis)
3. [Estrutura Geral de Resposta](#estrutura-geral-de-resposta)
4. [Indicadores Disponíveis](#indicadores-disponíveis)
   - [RSI (Relative Strength Index)](#rsi)
   - [MACD (Moving Average Convergence Divergence)](#macd)
   - [Bandas de Bollinger](#bandas-de-bollinger)
   - [Estocástico](#estocástico)
   - [ADX (Average Directional Index)](#adx)
   - [ATR (Average True Range)](#atr)
   - [M<PERSON><PERSON><PERSON> (SMA e EMA)](#medias-móveis)
   - [Ichimoku Cloud](#ichimoku-cloud)
5. [Uso em Tempo Real](#uso-em-tempo-real)
6. [Exemplos Comple<PERSON>](#exemplos-completos)

## Introdução

A biblioteca PyQuotex fornece uma implementação completa dos indicadores técnicos mais utilizados no trading. Cada indicador pode ser calculado em diferentes timeframes e pode ser monitorado em tempo real.

## Timeframes Disponíveis

Os seguintes timeframes estão disponíveis para todos os indicadores:

```python
valid_timeframes = {
    60: "1 minuto",
    300: "5 minutos",
    900: "15 minutos",
    1800: "30 minutos",
    3600: "1 hora",
    7200: "2 horas",
    14400: "4 horas",
    86400: "1 dia"
}
```

## Estrutura Geral de Resposta

Todos os indicadores retornam uma estrutura similar que inclui:

```python
{
    "indicator_values": [...],     # Lista de valores históricos
    "current": value,             # Valor atual do indicador
    "timeframe": timeframe,       # Timeframe utilizado
    "timestamps": [...],          # Lista de timestamps correspondentes
    "history_size": size         # Quantidade de valores históricos
}
```

## Indicadores Disponíveis

### RSI
O Índice de Força Relativa mede a velocidade e a magnitude dos movimentos direcionais dos preços.

```python
# Cálculo básico
rsi = await client.calculate_indicator(
    asset="EURUSD",
    indicator="RSI",
    params={"period": 14},
    timeframe=300  # 5 minutos
)

# Estrutura de resposta
{
    "rsi": [valores...],
    "current": 65.45,
    "history_size": 100,
    "timeframe": 300,
    "timestamps": [timestamps...]
}
```

### MACD
O MACD é um indicador de acompanhamento de tendência que mostra a relação entre duas médias móveis.

```python
# Cálculo básico
macd = await client.calculate_indicator(
    asset="EURUSD",
    indicator="MACD",
    params={
        "fast_period": 12,
        "slow_period": 26,
        "signal_period": 9
    },
    timeframe=900  # 15 minutos
)

# Estrutura de resposta
{
    "macd": [valores...],
    "signal": [valores...],
    "histogram": [valores...],
    "current": {
        "macd": 0.00125,
        "signal": 0.00100,
        "histogram": 0.00025
    },
    "timeframe": 900,
    "timestamps": [timestamps...]
}
```

### Bandas de Bollinger
As Bandas de Bollinger são um indicador de volatilidade que cria bandas superiores e inferiores ao redor do preço.

```python
# Cálculo básico
bollinger = await client.calculate_indicator(
    asset="EURUSD",
    indicator="BOLLINGER",
    params={
        "period": 20,
        "std": 2
    },
    timeframe=1800  # 30 minutos
)

# Estrutura de resposta
{
    "upper": [valores...],
    "middle": [valores...],
    "lower": [valores...],
    "current": {
        "upper": 1.1050,
        "middle": 1.1000,
        "lower": 1.0950
    },
    "timeframe": 1800,
    "timestamps": [timestamps...]
}
```

### Estocástico
O Oscilador Estocástico é um indicador de momento que compara o preço de fechamento com o intervalo de preços durante um período.

```python
# Cálculo básico
stochastic = await client.calculate_indicator(
    asset="EURUSD",
    indicator="STOCHASTIC",
    params={
        "k_period": 14,
        "d_period": 3
    },
    timeframe=3600  # 1 hora
)

# Estrutura de resposta
{
    "k": [valores...],
    "d": [valores...],
    "current": {
        "k": 75.5,
        "d": 72.3
    },
    "timeframe": 3600,
    "timestamps": [timestamps...]
}
```

### ADX
O Índice Direcional Médio mede a força de uma tendência.

```python
# Cálculo básico
adx = await client.calculate_indicator(
    asset="EURUSD",
    indicator="ADX",
    params={"period": 14},
    timeframe=7200  # 2 horas
)

# Estrutura de resposta
{
    "adx": [valores...],
    "plus_di": [valores...],
    "minus_di": [valores...],
    "current": {
        "adx": 25.5,
        "plus_di": 30.2,
        "minus_di": 20.1
    },
    "timeframe": 7200,
    "timestamps": [timestamps...]
}
```

### ATR
O Intervalo Verdadeiro Médio mede a volatilidade do mercado.

```python
# Cálculo básico
atr = await client.calculate_indicator(
    asset="EURUSD",
    indicator="ATR",
    params={"period": 14},
    timeframe=14400  # 4 horas
)

# Estrutura de resposta
{
    "atr": [valores...],
    "current": 0.00123,
    "history_size": 100,
    "timeframe": 14400,
    "timestamps": [timestamps...]
}
```

### Médias Móveis
As médias móveis suavizam os dados de preços para formar um indicador de tendência.

```python
# SMA (Simple Moving Average)
sma = await client.calculate_indicator(
    asset="EURUSD",
    indicator="SMA",
    params={"period": 20},
    timeframe=86400  # 1 dia
)

# EMA (Exponential Moving Average)
ema = await client.calculate_indicator(
    asset="EURUSD",
    indicator="EMA",
    params={"period": 20},
    timeframe=86400  # 1 dia
)

# Estrutura de resposta (igual para ambas)
{
    "sma": [valores...],  # ou "ema" para EMA
    "current": 1.1000,
    "history_size": 100,
    "timeframe": 86400,
    "timestamps": [timestamps...]
}
```

### Ichimoku Cloud
O Ichimoku Cloud é um indicador que mostra múltiplos níveis de suporte e resistência.

```python
# Cálculo básico
ichimoku = await client.calculate_indicator(
    asset="EURUSD",
    indicator="ICHIMOKU",
    params={
        "tenkan_period": 9,
        "kijun_period": 26,
        "senkou_b_period": 52
    },
    timeframe=3600  # 1 hora
)

# Estrutura de resposta
{
    "tenkan": [valores...],
    "kijun": [valores...],
    "senkou_a": [valores...],
    "senkou_b": [valores...],
    "chikou": [valores...],
    "current": {
        "tenkan": 1.1000,
        "kijun": 1.0990,
        "senkou_a": 1.1010,
        "senkou_b": 1.0980,
        "chikou": 1.0995
    },
    "timeframe": 3600,
    "timestamps": [timestamps...]
}
```

## Uso em Tempo Real

Todos os indicadores podem ser monitorados em tempo real usando a função `subscribe_indicator`:

```python
async def on_indicator_update(data):
    print(f"Tempo: {data['time']}")
    print(f"Valor atual: {data['value']}")
    print(f"Valores históricos: {data['all_values']}")

# Inscrever-se em atualizações de RSI
await client.subscribe_indicator(
    asset="EURUSD",
    indicator="RSI",
    params={"period": 14},
    callback=on_indicator_update,
    timeframe=300  # 5 minutos
)
```

## Exemplos Completos

### Exemplo 1: Análise Multi-Timeframe
```python
async def analyze_multi_timeframe():
    # Analisar RSI em múltiplos timeframes
    timeframes = [300, 900, 3600]  # 5m, 15m, 1h

    for tf in timeframes:
        rsi = await client.calculate_indicator(
            asset="EURUSD",
            indicator="RSI",
            params={"period": 14},
            timeframe=tf
        )
        print(f"RSI em {tf} segundos: {rsi['current']}")
```

### Exemplo 2: Análise de Tendência Completa
```python
async def analyze_trend():
    # Obter múltiplos indicadores
    macd = await client.calculate_indicator(
        asset="EURUSD",
        indicator="MACD",
        timeframe=3600
    )

    adx = await client.calculate_indicator(
        asset="EURUSD",
        indicator="ADX",
        timeframe=3600
    )

    bb = await client.calculate_indicator(
        asset="EURUSD",
        indicator="BOLLINGER",
        timeframe=3600
    )

    # Analisar tendência
    trend = {
        "macd_trend": "ALTISTA" if macd["current"]["histogram"] > 0 else "BAIXISTA",
        "adx_strength": "FORTE" if adx["current"]["adx"] > 25 else "FRACA",
        "volatility": bb["current"]["upper"] - bb["current"]["lower"]
    }

    print("Análise de tendência:", trend)
```

### Exemplo 3: Monitoramento em Tempo Real de Múltiplos Indicadores
```python
async def monitor_multiple_indicators():
    async def on_update(data):
        print(f"Indicador: {data['indicator']}")
        print(f"Valor atual: {data['value']}")
        print(f"Timeframe: {data['timeframe']}")
        print("---")

    # Monitorar RSI e MACD simultaneamente
    await asyncio.gather(
        client.subscribe_indicator("EURUSD", "RSI", callback=on_update, timeframe=300),
        client.subscribe_indicator("EURUSD", "MACD", callback=on_update, timeframe=300)
    )
```

