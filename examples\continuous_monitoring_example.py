#!/usr/bin/env python3
"""
مثال على استخدام ميزة المراقبة المستمرة في PyQuotex
Example of using the continuous monitoring feature in PyQuotex

هذا المثال يوضح كيفية استخدام الميزات الجديدة المضافة للمراقبة المستمرة
This example demonstrates how to use the new continuous monitoring features
"""

import asyncio
import sys
import os

# إضافة مسار المشروع الرئيسي
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import PyQuotexCLI


async def example_continuous_monitoring():
    """
    مثال على المراقبة المستمرة للأزواج
    Example of continuous asset monitoring
    """
    print("🚀 بدء مثال المراقبة المستمرة / Starting Continuous Monitoring Example")
    print("=" * 80)
    
    # إنشاء عميل PyQuotex
    cli = PyQuotexCLI()
    
    try:
        # بدء المراقبة المستمرة
        await cli.start_continuous_monitoring()
        
    except KeyboardInterrupt:
        print("\n✅ تم إيقاف المراقبة بواسطة المستخدم / Monitoring stopped by user")
    except Exception as e:
        print(f"❌ خطأ في المراقبة / Monitoring error: {e}")


async def example_persistent_connection():
    """
    مثال على الاتصال المستمر فقط
    Example of persistent connection only
    """
    print("🔗 بدء مثال الاتصال المستمر / Starting Persistent Connection Example")
    print("=" * 80)
    
    cli = PyQuotexCLI()
    
    try:
        # الحفاظ على الاتصال المستمر
        await cli.maintain_persistent_connection()
        
    except KeyboardInterrupt:
        print("\n✅ تم إيقاف الاتصال بواسطة المستخدم / Connection stopped by user")
    except Exception as e:
        print(f"❌ خطأ في الاتصال / Connection error: {e}")


async def example_asset_monitoring_only():
    """
    مثال على مراقبة الأزواج فقط (بدون اتصال مستمر)
    Example of asset monitoring only (without persistent connection)
    """
    print("📊 بدء مثال مراقبة الأزواج / Starting Asset Monitoring Example")
    print("=" * 80)
    
    cli = PyQuotexCLI()
    
    try:
        # التأكد من الاتصال أولاً
        if not await cli.client.check_connect():
            print("🔗 إنشاء اتصال أولي / Establishing initial connection...")
            check, reason = await cli._connect_with_retry(5)
            if not check:
                print(f"❌ فشل في الاتصال / Failed to connect: {reason}")
                return
        
        # مراقبة الأزواج فقط
        await cli.monitor_assets_continuously()
        
    except KeyboardInterrupt:
        print("\n✅ تم إيقاف مراقبة الأزواج / Asset monitoring stopped")
    except Exception as e:
        print(f"❌ خطأ في مراقبة الأزواج / Asset monitoring error: {e}")
    finally:
        # إغلاق الاتصال
        if cli.client and await cli.client.check_connect():
            await cli.client.close()


async def example_custom_monitoring():
    """
    مثال على مراقبة مخصصة مع إعدادات مختلفة
    Example of custom monitoring with different settings
    """
    print("⚙️ بدء مثال المراقبة المخصصة / Starting Custom Monitoring Example")
    print("=" * 80)
    
    cli = PyQuotexCLI()
    
    try:
        # التأكد من الاتصال
        if not await cli.client.check_connect():
            check, reason = await cli._connect_with_retry(3)
            if not check:
                print(f"❌ فشل في الاتصال / Failed to connect: {reason}")
                return
        
        print("✅ متصل بنجاح / Connected successfully")
        
        # مراقبة مخصصة مع فترات زمنية مختلفة
        update_interval = 15  # تحديث كل 15 ثانية
        max_assets_display = 20  # عرض أول 20 زوج فقط
        
        print(f"🔄 بدء المراقبة المخصصة (تحديث كل {update_interval} ثانية)")
        print(f"📊 عرض أول {max_assets_display} زوج")
        print("Press Ctrl+C to stop...")
        print("=" * 80)
        
        last_update = 0
        
        while True:
            current_time = asyncio.get_event_loop().time()
            
            if current_time - last_update >= update_interval:
                # مسح الشاشة
                os.system('cls' if os.name == 'nt' else 'clear')
                
                print(f"🔄 تحديث مخصص / Custom Update")
                print(f"⏰ الوقت: {asyncio.get_event_loop().time()}")
                print("=" * 80)
                
                # الحصول على بيانات الدفع
                payment_data = cli.client.get_payment()
                
                if payment_data:
                    # ترتيب الأزواج حسب الربح
                    sorted_pairs = sorted(
                        payment_data.items(),
                        key=lambda x: float(x[1].get("profit", {}).get("1M", 0) or 0),
                        reverse=True
                    )
                    
                    print(f"{'الزوج / Pair':<25} {'الحالة / Status':<10} {'ربح 1د / 1M':<12}")
                    print("-" * 60)
                    
                    for i, (pair_name, pair_data) in enumerate(sorted_pairs[:max_assets_display]):
                        status = "مفتوح / OPEN" if pair_data.get("open") else "مغلق / CLOSED"
                        profit_1m = pair_data.get("profit", {}).get("1M", "N/A")
                        emoji = "🟢" if pair_data.get("open") else "🔴"
                        
                        print(f"{emoji} {pair_name:<23} {status:<10} {profit_1m}%")
                    
                    print("-" * 60)
                    print(f"📊 عرض {min(len(sorted_pairs), max_assets_display)} من {len(sorted_pairs)} زوج")
                
                last_update = current_time
            
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        print("\n✅ تم إيقاف المراقبة المخصصة / Custom monitoring stopped")
    except Exception as e:
        print(f"❌ خطأ في المراقبة المخصصة / Custom monitoring error: {e}")
    finally:
        if cli.client and await cli.client.check_connect():
            await cli.client.close()


def main():
    """
    الدالة الرئيسية لتشغيل الأمثلة
    Main function to run examples
    """
    print("🎯 أمثلة المراقبة المستمرة لـ PyQuotex")
    print("🎯 PyQuotex Continuous Monitoring Examples")
    print("=" * 80)
    print("اختر مثالاً لتشغيله / Choose an example to run:")
    print("1. المراقبة المستمرة الكاملة / Full Continuous Monitoring")
    print("2. الاتصال المستمر فقط / Persistent Connection Only")
    print("3. مراقبة الأزواج فقط / Asset Monitoring Only")
    print("4. مراقبة مخصصة / Custom Monitoring")
    print("0. خروج / Exit")
    
    try:
        choice = input("\nأدخل اختيارك / Enter your choice (0-4): ").strip()
        
        if choice == "1":
            asyncio.run(example_continuous_monitoring())
        elif choice == "2":
            asyncio.run(example_persistent_connection())
        elif choice == "3":
            asyncio.run(example_asset_monitoring_only())
        elif choice == "4":
            asyncio.run(example_custom_monitoring())
        elif choice == "0":
            print("👋 وداعاً / Goodbye!")
        else:
            print("❌ اختيار غير صحيح / Invalid choice")
            
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء البرنامج / Program terminated")
    except Exception as e:
        print(f"❌ خطأ / Error: {e}")


if __name__ == "__main__":
    main()
