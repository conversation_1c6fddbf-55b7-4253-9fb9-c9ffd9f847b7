# Documentación de PyQuotex: Obtención de Datos del Mercado

## Índice
- [Obtención de Velas (Candles)](#obtención-de-velas-candles)
- [Datos en Tiempo Real](#datos-en-tiempo-real)
- [Sentimiento del Mercado](#sentimiento-del-mercado)
- [Precios en Tiempo Real](#precios-en-tiempo-real)
- [Señales de Trading](#señales-de-trading)
- [Lista de Activos](#lista-de-activos)
- [Verificación de Activos](#verificación-de-activos)

## Obtención de Velas (Candles)

### Obtener Velas Históricas
```python
async def get_candles(asset, end_from_time, offset, period):
    """
    Obtiene velas históricas para un activo específico.
    
    Parámetros:
    - asset: str - Nombre del activo (ej. "EURUSD_otc")
    - end_from_time: int - Timestamp de fin
    - offset: int - Desplazamiento en segundos (ej. 3600)
    - period: int - Período en segundos (ej. 60)
    """
    candles = await client.get_candles(asset, end_from_time, offset, period)
```

### Obtener Velas en Tiempo Real
```python
async def get_realtime_candle():
    asset = "EURUSD_otc"
    period = 5  # segundos [60, 120, 180, 240, 300, 600, 900, 1800, 3600, 14400, 86400]
    candles = await client.get_realtime_candles(asset_name, period)
```

## Datos en Tiempo Real

### Iniciar Stream de Datos
```python
def start_candles_stream(asset, period=0):
    client.start_candles_stream(asset, period)
    client.follow_candle(asset)
```

### Detener Stream de Datos
```python
def stop_candles_stream(asset):
    client.unsubscribe_realtime_candle(asset)
    client.unfollow_candle(asset)
```

## Sentimiento del Mercado

### Obtener Sentimiento en Tiempo Real
```python
async def get_realtime_sentiment(asset):
    """
    Obtiene el sentimiento del mercado para un activo.
    Retorna un diccionario con porcentajes de compra/venta.
    """
    sentiment = await client.get_realtime_sentiment(asset_name)
    # Ejemplo de respuesta: {"sentiment": {"sell": 40, "buy": 60}}
```

## Precios en Tiempo Real

### Obtener Precios en Tiempo Real
```python
async def get_realtime_price():
    asset = "EURJPY_otc"
    await client.start_realtime_price(asset, 60)
    candle_price = await client.get_realtime_price(asset_name)
    # Retorna último precio y timestamp
```

## Señales de Trading

### Obtener Señales de Trading
```python
async def get_signal_data():
    client.start_signals_data()
    signals = client.get_signal_data()
    # Retorna señales disponibles del mercado
```

## Lista de Activos

### Obtener Todos los Activos
```python
def get_all_asset_name():
    assets = client.get_all_asset_name()
    # Retorna lista de todos los activos disponibles
```

### Obtener Pagos (Payouts) por Activo
```python
def get_payment():
    all_data = client.get_payment()
    # Retorna información de payouts y estado de cada activo
```

## Verificación de Activos

### Verificar Disponibilidad de Activo
```python
async def check_asset_open(asset_name):
    """
    Verifica si un activo está disponible para operar.
    
    Retorna:
    - Tupla con (ID, nombre, estado_apertura)
    - estado_apertura es booleano (True si está abierto)
    """
    asset_status = await client.check_asset_open(asset_name)
```

### Obtener Activo con Fallback OTC
```python
async def get_available_asset(asset_name, force_open=True):
    """
    Obtiene un activo y verifica su disponibilidad.
    Si force_open es True y el activo está cerrado, intenta con versión OTC.
    """
    asset_name, asset_data = await client.get_available_asset(asset_name, force_open=True)
```

## Notas de Uso

- Los períodos disponibles para velas son: 5, 10, 15, 30, 60, 120, 180, 240, 300, 600, 900, 1800, 3600, 14400, 86400 segundos
- Para activos OTC, añadir sufijo "_otc" al nombre del activo
- Es recomendable verificar siempre el estado del activo antes de operar
- Los streams de datos deben ser detenidos cuando no se necesiten para optimizar recursos

## Ejemplo de Flujo Básico

```python
async def basic_market_data_flow():
    # Conectar
    check_connect, message = await client.connect()
    if check_connect:
        # Verificar activo
        asset = "EURUSD_otc"
        asset_name, asset_data = await client.get_available_asset(asset, force_open=True)
        
        if asset_data[2]:  # Si está abierto
            # Iniciar stream
            client.start_candles_stream(asset_name, 60)
            
            # Obtener datos
            candles = await client.get_realtime_candles(asset_name, 60)
            sentiment = await client.get_realtime_sentiment(asset_name)
            price = await client.get_realtime_price(asset_name)
            
            # Procesar datos...
            
            # Detener stream
            client.stop_candles_stream(asset_name)
    
    await cliente.close()
```