# PyQuotex - Forex Monitoring with JSON Export

## 🎯 الميزات الجديدة / New Features

### 🔄 مراقبة أزواج العملات التقليدية فقط / Traditional Forex Pairs Only
تم إضافة فلتر ذكي لعرض أزواج العملات التقليدية فقط واستثناء:
- العملات المشفرة (Bitcoin, Ethereum, إلخ)
- الأسهم (McDonald's, Microsoft, إلخ)  
- المعادن (Gold, Silver, إلخ)
- المؤشرات (<PERSON>, NASDAQ, إلخ)

### 💾 حفظ البيانات في JSON / JSON Data Export
- حفظ تلقائي للبيانات في ملفات JSON
- لا يتم عرض البيانات في الطرفية
- تنظيم البيانات بشكل منطقي ومفيد

### 📊 معلومات شاملة / Comprehensive Information
- معلومات الحساب (نوع الحساب، الرصيد)
- سجل الصفقات (المفتوحة والمغلقة)
- إحصائيات التداول
- بيانات أزواج العملات مع نسب الأرباح

## 🚀 كيفية الاستخدام / How to Use

### الأمر الجديد / New Command
```bash
# مراقبة أزواج العملات مع حفظ JSON (كل 5 دقائق)
python app.py forex-monitor-json

# تخصيص فترة الحفظ (بالثواني)
python app.py forex-monitor-json --save-interval 180  # كل 3 دقائق
python app.py forex-monitor-json --save-interval 600  # كل 10 دقائق
```

### الأوامر المتاحة / Available Commands
```bash
# عرض المساعدة
python app.py forex-monitor-json --help

# أمثلة مختلفة
python app.py forex-monitor-json --save-interval 60   # كل دقيقة
python app.py forex-monitor-json --save-interval 1800 # كل 30 دقيقة
```

## 📁 هيكل ملفات JSON / JSON File Structure

### مسار الحفظ / Save Path
```
data/forex_monitoring/forex_data_YYYYMMDD_HHMMSS.json
```

### هيكل البيانات / Data Structure
```json
{
  "collection_info": {
    "timestamp": "2025-07-21T23:45:30.123456",
    "data_type": "traditional_forex_pairs_only",
    "total_pairs": 25,
    "open_pairs": 18,
    "closed_pairs": 7,
    "high_profit_pairs": 12
  },
  "account_info": {
    "account_type": "PRACTICE",
    "balance": 5000.00,
    "profile": {
      "name": "User Name",
      "demo_balance": 5000.00,
      "live_balance": 0.00,
      "profile_id": "12345",
      "country": "Country Name",
      "timezone": "+03:00",
      "avatar": "avatar_url"
    }
  },
  "trading_history": {
    "open_trades": [],
    "closed_trades": [
      {
        "ticket": "*********",
        "asset": "EURUSD_otc",
        "amount": 50.0,
        "direction": "call",
        "profit_amount": 42.5,
        "open_time": "2025-07-21T23:30:00",
        "close_time": "2025-07-21T23:31:00",
        "status": "closed",
        "duration": 60,
        "profit_percentage": 85
      }
    ],
    "total_open": 0,
    "total_closed": 1,
    "statistics": {
      "total_profit": 42.5,
      "winning_trades": 1,
      "losing_trades": 0,
      "win_rate": 100.0
    }
  },
  "forex_pairs": [
    {
      "name": "EUR/USD (OTC)",
      "is_open": true,
      "profit_1m": "89",
      "profit_5m": "87",
      "profit_1m_float": 89.0,
      "profit_5m_float": 87.0,
      "turbo_payment": "85",
      "payment": "87"
    }
  ],
  "statistics": {
    "total_assets": 25,
    "open_assets": 18,
    "closed_assets": 7,
    "open_percentage": 72.0,
    "high_profit_assets": 12,
    "best_1m_profit": {
      "name": "EUR/USD (OTC)",
      "profit_1m": "89"
    },
    "best_5m_profit": {
      "name": "GBP/USD (OTC)", 
      "profit_5m": "88"
    }
  }
}
```

## 🎨 عرض الطرفية / Terminal Display

### أثناء التشغيل / During Operation
```
🔄 Forex Monitoring with JSON Saving Started
💾 Data will be saved to JSON files automatically
📊 Monitoring traditional forex pairs only
Press Ctrl+C to stop monitoring...
════════════════════════════════════════════════════════════════════════════════════════════════════

📊 Collecting data... (Save #1)
✅ Data saved successfully to: data/forex_monitoring/forex_data_20250721_234530.json
📈 Quick Summary:
   • Total Forex Pairs: 25
   • Open Pairs: 18
   • High Profit Pairs (≥80%): 12
   • Best 1M Profit: EUR/USD (OTC) (89%)
💰 Account: PRACTICE | Balance: 5000.0
⏰ Next save in 300 seconds...

⏳ Next save in 299 seconds... (Total saves: 1)
```

## 🔧 الفلاتر المطبقة / Applied Filters

### أزواج العملات المقبولة / Accepted Currency Pairs
- EUR/USD, GBP/USD, USD/JPY
- AUD/USD, USD/CAD, USD/CHF
- EUR/GBP, EUR/JPY, GBP/JPY
- وجميع أزواج العملات التقليدية الأخرى

### العملات المستثناة / Excluded Assets
```
❌ العملات المشفرة / Cryptocurrencies:
   Bitcoin, Ethereum, Litecoin, Dogecoin, Shiba Inu, إلخ

❌ الأسهم / Stocks:
   McDonald's, Microsoft, Intel, Boeing, إلخ

❌ المعادن / Commodities:
   Gold, Silver, Oil, Crude, إلخ

❌ المؤشرات / Indices:
   Dow Jones, NASDAQ, FTSE, S&P, إلخ
```

## 📊 الإحصائيات المتاحة / Available Statistics

### إحصائيات الأزواج / Pairs Statistics
- إجمالي أزواج العملات
- الأزواج المفتوحة/المغلقة
- نسبة الأزواج المفتوحة
- عدد الأزواج عالية الربح (≥80%)
- أفضل الأزواج ربحاً

### إحصائيات التداول / Trading Statistics
- إجمالي الربح/الخسارة
- عدد الصفقات الرابحة/الخاسرة
- معدل النجاح (Win Rate)
- الصفقات المفتوحة/المغلقة

## ⚙️ الإعدادات / Settings

### فترات الحفظ المقترحة / Recommended Save Intervals
```bash
# للمراقبة السريعة
--save-interval 60    # كل دقيقة

# للمراقبة العادية  
--save-interval 300   # كل 5 دقائق (افتراضي)

# للمراقبة طويلة المدى
--save-interval 1800  # كل 30 دقيقة
```

### متطلبات النظام / System Requirements
- مساحة تخزين كافية (كل ملف ~50-100 KB)
- اتصال مستقر بالإنترنت
- Python 3.7+
- جميع متطلبات PyQuotex

## 🛠️ استكشاف الأخطاء / Troubleshooting

### مشاكل الحفظ / Save Issues
```bash
# تحقق من الأذونات
ls -la data/forex_monitoring/

# إنشاء المجلد يدوياً
mkdir -p data/forex_monitoring
```

### مشاكل الاتصال / Connection Issues
```bash
# تشغيل مع تفاصيل أكثر
python app.py forex-monitor-json --save-interval 300 --verbose
```

### مشاكل البيانات / Data Issues
- تأكد من صحة بيانات الاعتماد
- تحقق من استقرار الاتصال
- راجع ملفات السجل

## 📈 أمثلة الاستخدام / Usage Examples

### للتداول اليومي / For Day Trading
```bash
# حفظ كل دقيقة للمراقبة السريعة
python app.py forex-monitor-json --save-interval 60
```

### للتحليل طويل المدى / For Long-term Analysis
```bash
# حفظ كل 30 دقيقة لجمع البيانات
python app.py forex-monitor-json --save-interval 1800
```

### للاختبار / For Testing
```bash
# حفظ كل 30 ثانية للاختبار
python app.py forex-monitor-json --save-interval 30
```

## 🔮 الميزات القادمة / Upcoming Features

- تحليل البيانات التاريخية
- تصدير إلى Excel/CSV
- تنبيهات مخصصة
- واجهة ويب للمراقبة
- تحليل الاتجاهات
