# PyQuotex - Continuous Monitoring Feature

## الميزات الجديدة المضافة / New Features Added

### 🔄 المراقبة المستمرة للأزواج / Continuous Asset Monitoring

تم إضافة ميزة جديدة للمراقبة المستمرة لجميع أزواج العملات المتاحة مع نسب الأرباح الخاصة بكل زوج.

A new feature has been added for continuous monitoring of all available currency pairs with their respective profit percentages.

## 🚀 كيفية الاستخدام / How to Use

### تشغيل المراقبة المستمرة / Start Continuous Monitoring

```bash
python app.py continuous-monitor
```

### الميزات المتاحة / Available Features

#### 1. الاتصال المستمر / Persistent Connection
- ✅ اتصال مستمر بمنصة Quotex
- ✅ إعادة الاتصال التلقائي في حالة انقطاع الشبكة
- ✅ مراقبة حالة الاتصال كل 5 ثوانٍ
- ✅ محاولات إعادة الاتصال التلقائية (حتى 10 محاولات)

#### 2. مراقبة الأزواج / Asset Monitoring
- 📊 عرض جميع الأزواج المتاحة مع حالتها (مفتوح/مغلق)
- 💰 نسب الأرباح لكل زوج (1 دقيقة و 5 دقائق)
- 🎨 عرض ملون للبيانات حسب نسبة الربح
- 📈 مؤشرات الاتجاه للأرباح
- 🏆 عرض أفضل الأزواج ربحاً
- 📊 إحصائيات شاملة

#### 3. التحديث التلقائي / Auto-Update
- 🔄 تحديث البيانات كل 30 ثانية
- 🖥️ مسح الشاشة وإعادة العرض للحصول على بيانات محدثة
- ⏰ عرض وقت آخر تحديث

## 🎨 ألوان العرض / Display Colors

### نسب الأرباح / Profit Percentages
- 🟢 **أخضر فاتح (≥80%)**: أرباح عالية جداً
- 🟢 **أخضر (70-79%)**: أرباح جيدة
- 🟡 **أصفر (60-69%)**: أرباح متوسطة
- 🔴 **أحمر (<60%)**: أرباح منخفضة

### حالة الأزواج / Asset Status
- 🟢 **مفتوح**: متاح للتداول
- 🔴 **مغلق**: غير متاح للتداول

### مؤشرات الاتجاه / Trend Indicators
- ↗ **صاعد**: ربح 1 دقيقة أعلى من 5 دقائق
- ↘ **هابط**: ربح 1 دقيقة أقل من 5 دقائق
- → **ثابت**: نفس نسبة الربح

## 📊 الإحصائيات المعروضة / Displayed Statistics

- إجمالي عدد الأزواج
- عدد الأزواج المفتوحة والمغلقة
- نسبة الأزواج المفتوحة
- عدد الأزواج عالية الربح (≥80%)
- أفضل زوج ربحاً لمدة 1 دقيقة
- أفضل زوج ربحاً لمدة 5 دقائق

## 🛠️ التحكم في التطبيق / Application Control

- **إيقاف المراقبة**: اضغط `Ctrl+C`
- **التحديث اليدوي**: سيتم التحديث تلقائياً كل 30 ثانية
- **إعادة الاتصال**: تتم تلقائياً في حالة انقطاع الاتصال

## 📝 مثال على الإخراج / Output Example

```
🔄 Continuous Asset Monitoring
📅 Last Update: 2025-07-21 23:30:15
════════════════════════════════════════════════════════════════════════════════════════════════════

Asset Name                     Status     1M Profit      5M Profit      Trend
────────────────────────────────────────────────────────────────────────────────────────────────────
🟢 EURUSD_otc                   OPEN       85%            82%            ↗
🟢 GBPUSD_otc                   OPEN       83%            80%            ↗
🟢 USDJPY_otc                   OPEN       78%            75%            ↗
🔴 AUDUSD_otc                   CLOSED     75%            73%            ↗

────────────────────────────────────────────────────────────────────────────────────────────────────
📊 STATISTICS
──────────────────────────────────────────────────
📈 Total Assets: 45 | Open: 32 (71.1%) | Closed: 13 | High Profit (≥80%): 8
🏆 Best 1M Profit: EURUSD_otc (85%)
🏆 Best 5M Profit: EURUSD_otc (82%)
──────────────────────────────────────────────────
🔄 Next update in 30 seconds...
```

## 🔧 متطلبات النظام / System Requirements

- Python 3.7+
- جميع المتطلبات الموجودة في `requirements.txt`
- اتصال مستقر بالإنترنت
- بيانات اعتماد صحيحة لمنصة Quotex

## ⚠️ ملاحظات مهمة / Important Notes

1. **الاستخدام المسؤول**: استخدم هذه الأداة بحكمة ولا تعتمد عليها كمصدر وحيد لاتخاذ قرارات التداول
2. **مراقبة الاتصال**: في حالة انقطاع الاتصال المتكرر، تحقق من اتصالك بالإنترنت
3. **استهلاك الموارد**: المراقبة المستمرة قد تستهلك موارد النظام، تأكد من إغلاق التطبيق عند عدم الحاجة إليه
4. **التحديثات**: يتم تحديث البيانات كل 30 ثانية لتجنب الضغط الزائد على الخادم

## 🆘 استكشاف الأخطاء / Troubleshooting

### مشاكل الاتصال / Connection Issues
```bash
# تشغيل مع تفاصيل أكثر
python app.py continuous-monitor --verbose
```

### مشاكل العرض / Display Issues
- تأكد من أن terminal يدعم ANSI colors
- في Windows، استخدم Windows Terminal أو PowerShell الحديث

### مشاكل الأداء / Performance Issues
- أغلق التطبيقات الأخرى التي تستهلك الذاكرة
- تأكد من استقرار اتصال الإنترنت
