# Documentação de Utilitários e Helpers - PyQuotex

## Índice
- [Processamento de Candlesticks](#processamento-de-candlesticks)
- [Manipulação de Timestamps](#manipulação-de-timestamps)
- [Cálculo de Expiração](#cálculo-de-expiração)
- [Formatação de Dados](#formatação-de-dados)
- [Tratamento de Erros](#tratamento-de-erros)

## Processamento de Candlesticks

### Funções Principais

#### `process_candles(history, period)`
Processa o histórico de candlesticks e os agrupa por período.

```python
def process_candles(history, period):
    candles = []
    current_candle = {
        'open': None,
        'high': float('-inf'),
        'low': float('inf'),
        'close': None,
        'start_time': None,
        'end_time': None,
        'ticks': 0
    }
    # ... processa e retorna os candlesticks agrupados
```

#### `get_color(candle)`
Determina a cor de um candlestick baseado em seus preços de abertura e fechamento.
```python
def get_color(candle):
    if candle['open'] < candle['close']:
        return 'green'
    elif candle['open'] > candle['close']:
        return 'red'
    return 'gray'
```

#### `process_tick(tick, candles, period=60)`
Processa ticks individuais e os incorpora aos candlesticks existentes.
```python
def process_tick(tick, candles, period=60):
    pair, timestamp, price, direction = tick
    # ... processa o tick e atualiza os candlesticks
```

## Manipulação de Timestamps

### Funções de Tempo

#### `get_timestamp()`
Obtém o timestamp atual em UTC.

#### `date_to_timestamp(dt)`
Converte um objeto datetime para timestamp.

#### `timestamp_to_date(timestamp)`
Converte um timestamp para objeto datetime.

#### `get_timestamp_days_ago(days)`
Calcula o timestamp de X dias atrás.
```python
def get_timestamp_days_ago(days):
    current_time = int(time.time())
    seconds_in_day = 86400
    return current_time - (days * seconds_in_day)
```

## Cálculo de Expiração

### Funções de Expiração

#### `get_expiration_time_quotex(timestamp, duration)`
Calcula o tempo de expiração para uma operação no Quotex.
```python
def get_expiration_time_quotex(timestamp, duration):
    now_date = datetime.fromtimestamp(timestamp)
    shift = 0
    if now_date.second >= 30:
        shift = 1
    exp_date = now_date.replace(second=0, microsecond=0)
    exp_date = exp_date + timedelta(minutes=int(duration / 60) + shift)
    return date_to_timestamp(exp_date)
```

#### `get_next_timeframe(timestamp, time_zone, timeframe, open_time=None)`
Calcula o próximo timeframe baseado em parâmetros específicos.
```python
def get_next_timeframe(timestamp, time_zone, timeframe: int, open_time: str = None) -> str:
    # ... calcula e retorna o próximo timeframe
```

## Formatação de Dados

### Funções de Formatação

#### `truncate(f, n)`
Trunca um número decimal para n decimais.
```python
def truncate(f, n):
    return math.floor(f * 10 ** n) / 10 ** n
```

#### `group_by_period(data, period)`
Agrupa dados por período específico.
```python
def group_by_period(data, period):
    grouped = defaultdict(list)
    for tick in data:
        timestamp = tick[0]
        timeframe = int(timestamp // period)
        grouped[timeframe].append(tick)
    return grouped
```

## Tratamento de Erros

### Sistema de Logging
O projeto utiliza o módulo `logging` do Python para o tratamento de erros e debugging.

```python
import logging

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
```

### Principais Tipos de Erros Tratados

1. **Erros de Conexão WebSocket**
```python
def on_error(self, wss, error):
    logger.error(error)
    global_value.websocket_error_reason = str(error)
    global_value.check_websocket_if_error = True
```

2. **Erros de Autenticação**
```python
if "authorization/reject" in str(message):
    logger.info("Token rejeitado, realizando reconexão automática.")
    global_value.check_rejected_connection = 1
```

3. **Erros em Operações de Trading**
```python
if global_value.websocket_error_reason == "not_money":
    self.api.account_balance = {"liveBalance": 0}
```

### Reconexão Automática
O sistema implementa um mecanismo de reconexão automática quando erros são detectados:

```python
async def connect(attempts=5):
    check, reason = await client.connect()
    if not check:
        attempt = 0
        while attempt <= attempts:
            # ... lógica de reconexão
            await asyncio.sleep(5)
```

### Melhores Práticas

1. **Tratamento de Erros Assíncronos**
   - Usar try/except em operações assíncronas
   - Implementar timeouts apropriados
   - Tratar erros de rede especificamente

2. **Logging**
   - Usar diferentes níveis de logging (DEBUG, INFO, ERROR)
   - Incluir informação contextual nos logs
   - Manter um registro de erros para análise

3. **Validação de Dados**
   - Verificar dados antes de processar
   - Validar parâmetros de entrada
   - Tratar casos específicos adequadamente