# Documentación WebSocket PyQuotex

## Tabla de Contenidos
- [Manejo de WebSocket](#manejo-de-websocket)
- [Suscripción a Streams](#suscripción-a-streams)
- [Streams Disponibles](#streams-disponibles)
- [Manejo de Eventos](#manejo-de-eventos)
- [Reconexión Automática](#reconexión-automática)

## Manejo de WebSocket

PyQuotex utiliza la librería `websocket-client` para establecer y mantener conexiones WebSocket con el servidor de Quotex. La implementación principal se encuentra en la clase `WebsocketClient`.

### Inicialización

```python
self.wss = websocket.WebSocketApp(
    self.api.wss_url,
    on_message=self.on_message,
    on_error=self.on_error,
    on_close=self.on_close,
    on_open=self.on_open,
    on_ping=self.on_ping,
    on_pong=self.on_pong,
    header=self.headers
)
```

## Suscripción a Streams

PyQuotex ofrece varios métodos para suscribirse a diferentes tipos de streams:

### Suscripción a Velas (Candles)
```python
def start_candles_stream(self, asset, period=0):
    self.api.current_asset = asset
    self.api.subscribe_realtime_candle(asset, period)
    self.api.follow_candle(asset)
```

### Suscripción a Sentimiento del Mercado
```python
async def start_realtime_sentiment(self, asset, period=0):
    self.start_candles_stream(asset, period)
    while True:
        if self.api.realtime_sentiment.get(asset):
            return self.api.realtime_sentiment[asset]
        await asyncio.sleep(0.2)
```

### Suscripción a Precios en Tiempo Real
```python
async def start_realtime_price(self, asset, period=0):
    self.start_candles_stream(asset, period)
    while True:
        if self.api.realtime_price.get(asset):
            return self.api.realtime_price
        await asyncio.sleep(0.2)
```

## Streams Disponibles

El sistema soporta los siguientes tipos de streams:

1. **Candles Stream**
   - Datos de velas en tiempo real
   - Histórico de velas
   - Velas en diferentes periodos de tiempo

2. **Price Stream**
   - Precios en tiempo real
   - Actualizaciones de precios por asset

3. **Sentiment Stream**
   - Sentimiento del mercado
   - Indicadores de compra/venta

4. **Signals Stream**
   - Señales de trading
   - Datos de indicadores

## Manejo de Eventos

### Eventos Principales

1. **on_message**
```python
def on_message(self, wss, message):
    """
    Procesa los mensajes entrantes del WebSocket
    - Maneja datos de autorización
    - Procesa datos de trading
    - Actualiza balances
    - Maneja señales y sentimiento del mercado
    """
```

2. **on_error**
```python
def on_error(self, wss, error):
    """
    Maneja errores en la conexión WebSocket
    - Registra errores
    - Actualiza estado de conexión
    """
```

3. **on_open**
```python
def on_open(self, wss):
    """
    Inicializa la conexión WebSocket
    - Envía mensajes iniciales
    - Configura streams básicos
    """
```

4. **on_close**
```python
def on_close(self, wss, close_status_code, close_msg):
    """
    Maneja el cierre de conexión
    - Actualiza estado de conexión
    - Prepara para reconexión si es necesario
    """
```

## Reconexión Automática

PyQuotex implementa un sistema robusto de reconexión automática:

### Manejo de Reconexión
```python
async def connect(attempts=5):
    check, reason = await client.connect()
    if not check:
        attempt = 0
        while attempt <= attempts:
            if not await client.check_connect():
                check, reason = await client.connect()
                if check:
                    print("Reconectado exitosamente!")
                    break
                else:
                    print("Error en la reconexión.")
                    attempt += 1
```

### Características de Reconexión

1. **Intentos Múltiples**
   - Número configurable de intentos de reconexión
   - Espera entre intentos

2. **Estado de Conexión**
   - Monitoreo constante del estado
   - Variables globales para tracking
   ```python
   check_websocket_if_connect = None
   check_websocket_if_error = False
   check_rejected_connection = False
   ```

3. **Restauración de Streams**
   - Re-suscripción automática a streams activos
   - Mantenimiento del estado de la aplicación

### Buenas Prácticas

1. **Manejo de Errores**
   - Implementar try-catch en operaciones críticas
   - Logging de errores y eventos importantes

2. **Estado de Conexión**
   - Verificar estado antes de operaciones
   - Mantener tiempo de espera adecuado entre reconexiones

3. **Limpieza de Recursos**
   - Cerrar conexiones apropiadamente
   - Liberar recursos al finalizar