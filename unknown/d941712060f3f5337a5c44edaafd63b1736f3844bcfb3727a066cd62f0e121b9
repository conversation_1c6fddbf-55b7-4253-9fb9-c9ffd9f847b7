# PyQuotex Documentation: Market Data Retrieval

## Index
- [Candle Data Retrieval](#candle-data-retrieval)
- [Real-time Data](#real-time-data)
- [Market Sentiment](#market-sentiment)
- [Real-time Prices](#real-time-prices)
- [Trading Signals](#trading-signals)
- [Asset List](#asset-list)
- [Asset Verification](#asset-verification)

## Candle Data Retrieval

### Get Historical Candles
```python
async def get_candles(asset, end_from_time, offset, period):
    """
    Retrieves historical candles for a specific asset.
    
    Parameters:
    - asset: str - Asset name (e.g., "EURUSD_otc")
    - end_from_time: int - End timestamp
    - offset: int - Offset in seconds (e.g., 3600)
    - period: int - Period in seconds (e.g., 60)
    """
    candles = await client.get_candles(asset, end_from_time, offset, period)
```

### Get Real-time Candles
```python
async def get_realtime_candle():
    asset = "EURUSD_otc"
    period = 5  # seconds [60, 120, 180, 240, 300, 600, 900, 1800, 3600, 14400, 86400]
    candles = await client.get_realtime_candles(asset_name, period)
```

## Real-time Data

### Start Data Stream
```python
def start_candles_stream(asset, period=0):
    client.start_candles_stream(asset, period)
    client.follow_candle(asset)
```

### Stop Data Stream
```python
def stop_candles_stream(asset):
    client.unsubscribe_realtime_candle(asset)
    client.unfollow_candle(asset)
```

## Market Sentiment

### Get Real-time Sentiment
```python
async def get_realtime_sentiment(asset):
    """
    Gets market sentiment for an asset.
    Returns a dictionary with buy/sell percentages.
    """
    sentiment = await client.get_realtime_sentiment(asset_name)
    # Example response: {"sentiment": {"sell": 40, "buy": 60}}
```

## Real-time Prices

### Get Real-time Prices
```python
async def get_realtime_price():
    asset = "EURJPY_otc"
    await client.start_realtime_price(asset, 60)
    candle_price = await client.get_realtime_price(asset_name)
    # Returns latest price and timestamp
```

## Trading Signals

### Get Trading Signals
```python
async def get_signal_data():
    client.start_signals_data()
    signals = client.get_signal_data()
    # Returns available market signals
```

## Asset List

### Get All Assets
```python
def get_all_asset_name():
    assets = client.get_all_asset_name()
    # Returns list of all available assets
```

### Get Asset Payouts
```python
def get_payment():
    all_data = client.get_payment()
    # Returns payout information and status for each asset
```

## Asset Verification

### Check Asset Availability
```python
async def check_asset_open(asset_name):
    """
    Verifies if an asset is available for trading.
    
    Returns:
    - Tuple with (ID, name, open_status)
    - open_status is boolean (True if open)
    """
    asset_status = await client.check_asset_open(asset_name)
```

### Get Asset with OTC Fallback
```python
async def get_available_asset(asset_name, force_open=True):
    """
    Gets an asset and verifies its availability.
    If force_open is True and asset is closed, tries OTC version.
    """
    asset_name, asset_data = await client.get_available_asset(asset_name, force_open=True)
```

## Usage Notes

- Available candle periods are: 5, 10, 15, 30, 60, 120, 180, 240, 300, 600, 900, 1800, 3600, 14400, 86400 seconds
- For OTC assets, add "_otc" suffix to asset name
- Always verify asset status before trading
- Data streams should be stopped when no longer needed to optimize resources

## Basic Flow Example

```python
async def basic_market_data_flow():
    # Connect
    check_connect, message = await client.connect()
    if check_connect:
        # Verify asset
        asset = "EURUSD_otc"
        asset_name, asset_data = await client.get_available_asset(asset, force_open=True)
        
        if asset_data[2]:  # If open
            # Start stream
            client.start_candles_stream(asset_name, 60)
            
            # Get data
            candles = await client.get_realtime_candles(asset_name, 60)
            sentiment = await client.get_realtime_sentiment(asset_name)
            price = await client.get_realtime_price(asset_name)
            
            # Process data...
            
            # Stop stream
            client.stop_candles_stream(asset_name)
    
    await cliente.close()
```