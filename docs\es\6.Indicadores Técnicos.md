# Documentación de Indicadores Técnicos en PyQuotex

## Índice
1. [Introducción](#introducción)
2. [Timeframes Disponibles](#timeframes-disponibles)
3. [Estructura General de Respuesta](#estructura-general-de-respuesta)
4. [Indicadores Disponibles](#indicadores-disponibles)
   - [RSI (Relative Strength Index)](#rsi)
   - [MACD (Moving Average Convergence Divergence)](#macd)
   - [Bandas de Bollinger](#bandas-de-bollinger)
   - [Estocástico](#estocástico)
   - [ADX (Average Directional Index)](#adx)
   - [ATR (Average True Range)](#atr)
   - [Medias Móviles (SMA y EMA)](#medias-móviles)
   - [Ichimoku Cloud](#ichimoku-cloud)
5. [Uso en Tiempo Real](#uso-en-tiempo-real)
6. [Ejemplos Completos](#ejemplos-completos)

## Introducción

La biblioteca PyQuotex proporciona una implementación completa de los indicadores técnicos más utilizados en el trading. Cada indicador puede ser calculado en diferentes timeframes y puede ser monitoreado en tiempo real.

## Timeframes Disponibles

Los siguientes timeframes están disponibles para todos los indicadores:

```python
valid_timeframes = {
    60: "1 minuto",
    300: "5 minutos",
    900: "15 minutos",
    1800: "30 minutos",
    3600: "1 hora",
    7200: "2 horas",
    14400: "4 horas",
    86400: "1 día"
}
```

## Estructura General de Respuesta

Todos los indicadores devuelven una estructura similar que incluye:

```python
{
    "indicator_values": [...],     # Lista de valores históricos
    "current": value,             # Valor actual del indicador
    "timeframe": timeframe,       # Timeframe utilizado
    "timestamps": [...],          # Lista de timestamps correspondientes
    "history_size": size         # Cantidad de valores históricos
}
```

## Indicadores Disponibles

### RSI
El Índice de Fuerza Relativa mide la velocidad y magnitud de los movimientos direccionales de los precios.

```python
# Cálculo básico
rsi = await client.calculate_indicator(
    asset="EURUSD",
    indicator="RSI",
    params={"period": 14},
    timeframe=300  # 5 minutos
)

# Estructura de respuesta
{
    "rsi": [valores...],
    "current": 65.45,
    "history_size": 100,
    "timeframe": 300,
    "timestamps": [timestamps...]
}
```

### MACD
El MACD es un indicador de seguimiento de tendencia que muestra la relación entre dos medias móviles.

```python
# Cálculo básico
macd = await client.calculate_indicator(
    asset="EURUSD",
    indicator="MACD",
    params={
        "fast_period": 12,
        "slow_period": 26,
        "signal_period": 9
    },
    timeframe=900  # 15 minutos
)

# Estructura de respuesta
{
    "macd": [valores...],
    "signal": [valores...],
    "histogram": [valores...],
    "current": {
        "macd": 0.00125,
        "signal": 0.00100,
        "histogram": 0.00025
    },
    "timeframe": 900,
    "timestamps": [timestamps...]
}
```

### Bandas de Bollinger
Las Bandas de Bollinger son un indicador de volatilidad que crea bandas superiores e inferiores alrededor del precio.

```python
# Cálculo básico
bollinger = await client.calculate_indicator(
    asset="EURUSD",
    indicator="BOLLINGER",
    params={
        "period": 20,
        "std": 2
    },
    timeframe=1800  # 30 minutos
)

# Estructura de respuesta
{
    "upper": [valores...],
    "middle": [valores...],
    "lower": [valores...],
    "current": {
        "upper": 1.1050,
        "middle": 1.1000,
        "lower": 1.0950
    },
    "timeframe": 1800,
    "timestamps": [timestamps...]
}
```

### Estocástico
El Oscilador Estocástico es un indicador de momento que compara el precio de cierre con el rango de precios durante un período.

```python
# Cálculo básico
stochastic = await client.calculate_indicator(
    asset="EURUSD",
    indicator="STOCHASTIC",
    params={
        "k_period": 14,
        "d_period": 3
    },
    timeframe=3600  # 1 hora
)

# Estructura de respuesta
{
    "k": [valores...],
    "d": [valores...],
    "current": {
        "k": 75.5,
        "d": 72.3
    },
    "timeframe": 3600,
    "timestamps": [timestamps...]
}
```

### ADX
El Índice Direccional Medio mide la fuerza de una tendencia.

```python
# Cálculo básico
adx = await client.calculate_indicator(
    asset="EURUSD",
    indicator="ADX",
    params={"period": 14},
    timeframe=7200  # 2 horas
)

# Estructura de respuesta
{
    "adx": [valores...],
    "plus_di": [valores...],
    "minus_di": [valores...],
    "current": {
        "adx": 25.5,
        "plus_di": 30.2,
        "minus_di": 20.1
    },
    "timeframe": 7200,
    "timestamps": [timestamps...]
}
```

### ATR
El Rango Verdadero Medio mide la volatilidad del mercado.

```python
# Cálculo básico
atr = await client.calculate_indicator(
    asset="EURUSD",
    indicator="ATR",
    params={"period": 14},
    timeframe=14400  # 4 horas
)

# Estructura de respuesta
{
    "atr": [valores...],
    "current": 0.00123,
    "history_size": 100,
    "timeframe": 14400,
    "timestamps": [timestamps...]
}
```

### Medias Móviles
Las medias móviles suavizan los datos de precios para formar un indicador de tendencia.

```python
# SMA (Simple Moving Average)
sma = await client.calculate_indicator(
    asset="EURUSD",
    indicator="SMA",
    params={"period": 20},
    timeframe=86400  # 1 día
)

# EMA (Exponential Moving Average)
ema = await client.calculate_indicator(
    asset="EURUSD",
    indicator="EMA",
    params={"period": 20},
    timeframe=86400  # 1 día
)

# Estructura de respuesta (igual para ambas)
{
    "sma": [valores...],  # o "ema" para EMA
    "current": 1.1000,
    "history_size": 100,
    "timeframe": 86400,
    "timestamps": [timestamps...]
}
```

### Ichimoku Cloud
El Ichimoku Cloud es un indicador que muestra múltiples niveles de soporte y resistencia.

```python
# Cálculo básico
ichimoku = await client.calculate_indicator(
    asset="EURUSD",
    indicator="ICHIMOKU",
    params={
        "tenkan_period": 9,
        "kijun_period": 26,
        "senkou_b_period": 52
    },
    timeframe=3600  # 1 hora
)

# Estructura de respuesta
{
    "tenkan": [valores...],
    "kijun": [valores...],
    "senkou_a": [valores...],
    "senkou_b": [valores...],
    "chikou": [valores...],
    "current": {
        "tenkan": 1.1000,
        "kijun": 1.0990,
        "senkou_a": 1.1010,
        "senkou_b": 1.0980,
        "chikou": 1.0995
    },
    "timeframe": 3600,
    "timestamps": [timestamps...]
}
```

## Uso en Tiempo Real

Todos los indicadores pueden ser monitoreados en tiempo real usando la función `subscribe_indicator`:

```python
async def on_indicator_update(data):
    print(f"Tiempo: {data['time']}")
    print(f"Valor actual: {data['value']}")
    print(f"Valores históricos: {data['all_values']}")

# Suscribirse a actualizaciones de RSI
await client.subscribe_indicator(
    asset="EURUSD",
    indicator="RSI",
    params={"period": 14},
    callback=on_indicator_update,
    timeframe=300  # 5 minutos
)
```

## Ejemplos Completos

### Ejemplo 1: Análisis Multi-Timeframe
```python
async def analyze_multi_timeframe():
    # Analizar RSI en múltiples timeframes
    timeframes = [300, 900, 3600]  # 5m, 15m, 1h
    
    for tf in timeframes:
        rsi = await client.calculate_indicator(
            asset="EURUSD",
            indicator="RSI",
            params={"period": 14},
            timeframe=tf
        )
        print(f"RSI en {tf} segundos: {rsi['current']}")

### Ejemplo 2: Análisis de Tendencia Completo
```python
async def analyze_trend():
    # Obtener múltiples indicadores
    macd = await client.calculate_indicator(
        asset="EURUSD",
        indicator="MACD",
        timeframe=3600
    )
    
    adx = await client.calculate_indicator(
        asset="EURUSD",
        indicator="ADX",
        timeframe=3600
    )
    
    bb = await client.calculate_indicator(
        asset="EURUSD",
        indicator="BOLLINGER",
        timeframe=3600
    )
    
    # Analizar tendencia
    trend = {
        "macd_trend": "ALCISTA" if macd["current"]["histogram"] > 0 else "BAJISTA",
        "adx_strength": "FUERTE" if adx["current"]["adx"] > 25 else "DÉBIL",
        "volatility": bb["current"]["upper"] - bb["current"]["lower"]
    }
    
    print("Análisis de tendencia:", trend)
```

### Ejemplo 3: Monitoreo en Tiempo Real de Múltiples Indicadores
```python
async def monitor_multiple_indicators():
    async def on_update(data):
        print(f"Indicador: {data['indicator']}")
        print(f"Valor actual: {data['value']}")
        print(f"Timeframe: {data['timeframe']}")
        print("---")

    # Monitorear RSI y MACD simultáneamente
    await asyncio.gather(
        client.subscribe_indicator("EURUSD", "RSI", callback=on_update, timeframe=300),
        client.subscribe_indicator("EURUSD", "MACD", callback=on_update, timeframe=300)
    )
```