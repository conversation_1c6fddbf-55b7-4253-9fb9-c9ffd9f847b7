#!/usr/bin/env python3
"""
اختبار ميزة مراقبة أزواج العملات مع حفظ JSON
Test forex monitoring with JSON saving feature

هذا المثال يوضح كيفية اختبار الميزات الجديدة
This example demonstrates how to test the new features
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# إضافة مسار المشروع الرئيسي
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import PyQuotexCLI, is_traditional_forex_pair, filter_traditional_forex_pairs


def test_forex_filter():
    """
    اختبار فلتر أزواج العملات التقليدية
    Test traditional forex pairs filter
    """
    print("🧪 Testing Forex Filter...")
    print("=" * 50)
    
    # أمثلة للاختبار
    test_assets = [
        "EUR/USD (OTC)",
        "GBP/USD (OTC)", 
        "Bitcoin (OTC)",
        "Gold (OTC)",
        "McDonald's (OTC)",
        "USD/JPY (OTC)",
        "Ethereum (OTC)",
        "Silver (OTC)",
        "NASDAQ 100",
        "AUD/CAD (OTC)",
        "Dogecoin (OTC)",
        "Microsoft (OTC)"
    ]
    
    print("Testing asset filtering:")
    for asset in test_assets:
        is_forex = is_traditional_forex_pair(asset)
        status = "✅ FOREX" if is_forex else "❌ FILTERED"
        print(f"{status:<12} {asset}")
    
    print("\n" + "=" * 50)


def analyze_json_file(filepath):
    """
    تحليل ملف JSON المحفوظ
    Analyze saved JSON file
    """
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📊 Analysis of: {os.path.basename(filepath)}")
        print("=" * 60)
        
        # معلومات عامة
        if 'collection_info' in data:
            info = data['collection_info']
            print(f"📅 Timestamp: {info.get('timestamp', 'N/A')}")
            print(f"📈 Total Pairs: {info.get('total_pairs', 0)}")
            print(f"🟢 Open Pairs: {info.get('open_pairs', 0)}")
            print(f"🔴 Closed Pairs: {info.get('closed_pairs', 0)}")
            print(f"⭐ High Profit Pairs: {info.get('high_profit_pairs', 0)}")
        
        # معلومات الحساب
        if 'account_info' in data:
            account = data['account_info']
            print(f"\n💰 Account Type: {account.get('account_type', 'N/A')}")
            print(f"💵 Balance: {account.get('balance', 'N/A')}")
            if 'profile' in account:
                profile = account['profile']
                print(f"👤 Name: {profile.get('name', 'N/A')}")
                print(f"🌍 Country: {profile.get('country', 'N/A')}")
        
        # إحصائيات التداول
        if 'trading_history' in data:
            trading = data['trading_history']
            print(f"\n📊 Trading Statistics:")
            print(f"   Open Trades: {trading.get('total_open', 0)}")
            print(f"   Closed Trades: {trading.get('total_closed', 0)}")
            
            if 'statistics' in trading:
                stats = trading['statistics']
                print(f"   Total Profit: {stats.get('total_profit', 0)}")
                print(f"   Win Rate: {stats.get('win_rate', 0):.1f}%")
        
        # أفضل الأزواج
        if 'statistics' in data and data['statistics'].get('best_1m_profit'):
            best = data['statistics']['best_1m_profit']
            print(f"\n🏆 Best 1M Profit: {best.get('name', 'N/A')} ({best.get('profit_1m', 'N/A')}%)")
        
        # عدد أزواج العملات
        if 'forex_pairs' in data:
            pairs_count = len(data['forex_pairs'])
            print(f"\n📋 Forex Pairs Found: {pairs_count}")
            
            # عرض أول 5 أزواج
            print("   Top 5 Pairs:")
            for i, pair in enumerate(data['forex_pairs'][:5]):
                status = "🟢" if pair.get('is_open') else "🔴"
                print(f"   {i+1}. {status} {pair.get('name', 'N/A')} - 1M: {pair.get('profit_1m', 'N/A')}%")
        
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing file: {e}")
        return False


async def test_data_collection():
    """
    اختبار جمع البيانات
    Test data collection
    """
    print("🧪 Testing Data Collection...")
    print("=" * 50)
    
    try:
        cli = PyQuotexCLI()
        
        # محاولة الاتصال
        print("🔗 Attempting to connect...")
        if not await cli.client.check_connect():
            check, reason = await cli._connect_with_retry(3)
            if not check:
                print(f"❌ Failed to connect: {reason}")
                return False
        
        print("✅ Connected successfully!")
        
        # جمع البيانات
        print("📊 Collecting data...")
        data = await cli.collect_and_save_data()
        
        if 'error' in data:
            print(f"❌ Error collecting data: {data['error']}")
            return False
        
        # حفظ البيانات للاختبار
        test_filename = f"data/test_forex_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        saved_file = cli.save_data_to_json(data, test_filename)
        
        if saved_file:
            print(f"✅ Test data saved to: {saved_file}")
            
            # تحليل البيانات المحفوظة
            print("\n📊 Analyzing saved data...")
            analyze_json_file(saved_file)
            
            return True
        else:
            print("❌ Failed to save test data")
            return False
            
    except Exception as e:
        print(f"❌ Error in data collection test: {e}")
        return False
    finally:
        # إغلاق الاتصال
        if cli.client and await cli.client.check_connect():
            await cli.client.close()


def test_json_structure():
    """
    اختبار هيكل JSON المتوقع
    Test expected JSON structure
    """
    print("🧪 Testing JSON Structure...")
    print("=" * 50)
    
    # هيكل JSON المتوقع
    expected_structure = {
        "collection_info": {
            "timestamp": "string",
            "data_type": "traditional_forex_pairs_only",
            "total_pairs": "number",
            "open_pairs": "number", 
            "closed_pairs": "number",
            "high_profit_pairs": "number"
        },
        "account_info": {
            "account_type": "string",
            "balance": "number",
            "profile": "object"
        },
        "trading_history": {
            "open_trades": "array",
            "closed_trades": "array",
            "statistics": "object"
        },
        "forex_pairs": "array",
        "statistics": "object"
    }
    
    print("Expected JSON structure:")
    print(json.dumps(expected_structure, indent=2))
    print("=" * 50)


async def run_short_monitoring_test():
    """
    تشغيل اختبار قصير للمراقبة
    Run short monitoring test
    """
    print("🧪 Running Short Monitoring Test...")
    print("=" * 50)
    
    try:
        cli = PyQuotexCLI()
        
        # اختبار لمدة 30 ثانية مع حفظ كل 10 ثوان
        print("⏰ Running 30-second test with 10-second save intervals...")
        
        # إنشاء مهمة المراقبة
        monitoring_task = asyncio.create_task(
            cli.start_forex_monitoring_with_json(save_interval=10)
        )
        
        # تشغيل لمدة 30 ثانية
        await asyncio.sleep(30)
        
        # إيقاف المراقبة
        monitoring_task.cancel()
        
        print("✅ Short monitoring test completed!")
        
        # البحث عن الملفات المحفوظة
        data_dir = "data/forex_monitoring"
        if os.path.exists(data_dir):
            files = [f for f in os.listdir(data_dir) if f.endswith('.json')]
            print(f"📁 Found {len(files)} saved files:")
            for file in files[-3:]:  # عرض آخر 3 ملفات
                print(f"   📄 {file}")
                filepath = os.path.join(data_dir, file)
                analyze_json_file(filepath)
        
    except Exception as e:
        print(f"❌ Error in monitoring test: {e}")


def main():
    """
    الدالة الرئيسية للاختبار
    Main test function
    """
    print("🎯 PyQuotex Forex JSON Monitoring Tests")
    print("🎯 اختبارات مراقبة أزواج العملات مع JSON")
    print("=" * 80)
    
    print("اختر اختباراً لتشغيله / Choose a test to run:")
    print("1. اختبار فلتر أزواج العملات / Test Forex Filter")
    print("2. اختبار جمع البيانات / Test Data Collection") 
    print("3. اختبار هيكل JSON / Test JSON Structure")
    print("4. اختبار مراقبة قصير / Short Monitoring Test")
    print("5. تحليل ملف JSON موجود / Analyze Existing JSON File")
    print("0. خروج / Exit")
    
    try:
        choice = input("\nأدخل اختيارك / Enter your choice (0-5): ").strip()
        
        if choice == "1":
            test_forex_filter()
        elif choice == "2":
            asyncio.run(test_data_collection())
        elif choice == "3":
            test_json_structure()
        elif choice == "4":
            asyncio.run(run_short_monitoring_test())
        elif choice == "5":
            filepath = input("أدخل مسار ملف JSON / Enter JSON file path: ").strip()
            if os.path.exists(filepath):
                analyze_json_file(filepath)
            else:
                print("❌ File not found")
        elif choice == "0":
            print("👋 وداعاً / Goodbye!")
        else:
            print("❌ اختيار غير صحيح / Invalid choice")
            
    except KeyboardInterrupt:
        print("\n👋 تم إنهاء الاختبار / Test terminated")
    except Exception as e:
        print(f"❌ خطأ / Error: {e}")


if __name__ == "__main__":
    main()
