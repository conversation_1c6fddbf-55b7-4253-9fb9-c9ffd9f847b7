# النظام المتكامل لمراقبة أزواج العملات / Integrated Forex Monitoring System

## 🎯 النظام المتكامل الجديد / New Integrated System

تم تطوير نظام متكامل واحد يعمل بالتسلسل التالي:

1. **جلب قائمة أزواج العملات التقليدية** وحفظها في ملف منفصل
2. **تحديث نسب الأرباح كل دقيقة** في نفس الملف
3. **حفظ معلومات الحساب** في ملف منفصل
4. **حفظ سجل الصفقات** في ملف منفصل
5. **كل شيء يعمل في أمر واحد متكامل**

## 🚀 الأمر الجديد / New Command

```bash
python app.py forex-system
```

## 📁 هيكل الملفات / File Structure

النظام ينشئ 3 ملفات منفصلة في مجلد `data/forex_monitoring/`:

### 1. forex_pairs.json
**ملف أزواج العملات مع التحديث المستمر**
```json
{
  "initialization_info": {
    "timestamp": "2025-07-22T00:15:30.123456",
    "total_pairs": 25,
    "data_type": "traditional_forex_pairs_only"
  },
  "last_update_info": {
    "timestamp": "2025-07-22T00:16:30.123456",
    "update_number": 1,
    "total_pairs": 25,
    "open_pairs": 18,
    "closed_pairs": 7,
    "high_profit_pairs": 12
  },
  "forex_pairs": [
    {
      "name": "EUR/USD (OTC)",
      "is_open": true,
      "profit_1m": "89",
      "profit_5m": "87",
      "turbo_payment": "85",
      "payment": "87",
      "last_update": "2025-07-22T00:16:30.123456"
    }
  ]
}
```

### 2. account_info.json
**معلومات الحساب**
```json
{
  "account_type": "PRACTICE",
  "balance": 5000.00,
  "profile": {
    "name": "User Name",
    "demo_balance": 5000.00,
    "live_balance": 0.00,
    "profile_id": "12345",
    "country": "Country Name",
    "timezone": "+03:00",
    "avatar": "avatar_url"
  },
  "timestamp": "2025-07-22T00:15:30.123456"
}
```

### 3. trading_history.json
**سجل الصفقات**
```json
{
  "open_trades": [],
  "closed_trades": [
    {
      "ticket": "*********",
      "asset": "EURUSD_otc",
      "amount": 50.0,
      "direction": "call",
      "profit_amount": 42.5,
      "open_time": "2025-07-22T00:10:00",
      "close_time": "2025-07-22T00:11:00",
      "status": "closed",
      "duration": 60,
      "profit_percentage": 85
    }
  ],
  "total_open": 0,
  "total_closed": 1,
  "statistics": {
    "total_profit": 42.5,
    "winning_trades": 1,
    "losing_trades": 0,
    "win_rate": 100.0
  },
  "timestamp": "2025-07-22T00:15:30.123456"
}
```

## 🔄 كيف يعمل النظام / How the System Works

### الخطوة 1: التهيئة الأولية
- الاتصال بمنصة Quotex
- جلب جميع أزواج العملات المتاحة
- تصفية أزواج العملات التقليدية فقط (استثناء العملات المشفرة والأسهم والمعادن)
- حفظ القائمة الأولية في `forex_pairs.json`

### الخطوة 2: حفظ البيانات الإضافية
- جلب معلومات الحساب وحفظها في `account_info.json`
- جلب سجل الصفقات وحفظه في `trading_history.json`

### الخطوة 3: التحديث المستمر
- تحديث نسب الأرباح لجميع الأزواج **كل دقيقة**
- تحديث ملف `forex_pairs.json` بالبيانات الجديدة
- الحفاظ على الاتصال المستمر مع إعادة الاتصال التلقائي

## 🎨 عرض النظام / System Display

```
✅ Connected to Quotex platform successfully!
🔄 Starting integrated forex monitoring system...
════════════════════════════════════════════════════════════════════════════════════════════════════

📊 Step 1: Loading available forex pairs...
✅ Initial forex pairs saved to: data/forex_monitoring/forex_pairs.json
📈 Found 25 traditional forex pairs

💰 Step 2: Saving account and trading data...
💰 Account info saved to: data/forex_monitoring/account_info.json
📊 Trading history saved to: data/forex_monitoring/trading_history.json

🔄 Step 3: Starting continuous updates every minute...
Press Ctrl+C to stop the system...
════════════════════════════════════════════════════════════════════════════════════════════════════

🔄 Update #1 - 00:16:30
✅ Updated 25 forex pairs
📊 Open: 18 | Closed: 7 | High Profit: 12
⏰ Next update in 60 seconds...

🔄 Update #2 - 00:17:30
✅ Updated 25 forex pairs
📊 Open: 19 | Closed: 6 | High Profit: 14
⏰ Next update in 60 seconds...
```

## 🔧 الميزات الرئيسية / Key Features

### ✅ فصل البيانات
- **ملف منفصل لأزواج العملات** مع التحديث المستمر
- **ملف منفصل لمعلومات الحساب**
- **ملف منفصل لسجل الصفقات**

### ✅ التحديث الذكي
- تحديث نسب الأرباح **كل دقيقة واحدة**
- الحفاظ على البيانات الأولية
- إضافة معلومات التحديث مع كل دورة

### ✅ الفلترة الذكية
- **أزواج العملات التقليدية فقط**
- استثناء العملات المشفرة والأسهم والمعادن
- ترتيب أبجدي للأزواج

### ✅ الاتصال المستقر
- اتصال مستمر مع إعادة الاتصال التلقائي
- مراقبة حالة الاتصال
- معالجة الأخطاء والاستثناءات

## 📊 الإحصائيات المتاحة / Available Statistics

### في ملف forex_pairs.json
- إجمالي عدد الأزواج
- عدد الأزواج المفتوحة/المغلقة
- عدد الأزواج عالية الربح (≥80%)
- رقم التحديث الحالي
- وقت آخر تحديث

### في ملف trading_history.json
- إجمالي الربح/الخسارة
- عدد الصفقات الرابحة/الخاسرة
- معدل النجاح (Win Rate)
- تفاصيل كل صفقة

## 🛠️ الاستخدام / Usage

### تشغيل النظام
```bash
# تشغيل النظام المتكامل
python app.py forex-system

# إيقاف النظام
# اضغط Ctrl+C
```

### مراقبة الملفات
```bash
# مراقبة ملف أزواج العملات
tail -f data/forex_monitoring/forex_pairs.json

# عرض معلومات الحساب
cat data/forex_monitoring/account_info.json

# عرض سجل الصفقات
cat data/forex_monitoring/trading_history.json
```

## 🔍 الأزواج المشمولة / Included Pairs

### ✅ أزواج العملات التقليدية
- EUR/USD, GBP/USD, USD/JPY
- AUD/USD, USD/CAD, USD/CHF
- EUR/GBP, EUR/JPY, GBP/JPY
- وجميع أزواج العملات الأخرى

### ❌ الأصول المستثناة
- العملات المشفرة (Bitcoin, Ethereum, إلخ)
- الأسهم (McDonald's, Microsoft, إلخ)
- المعادن (Gold, Silver, إلخ)
- المؤشرات (Dow Jones, NASDAQ, إلخ)

## 🚨 ملاحظات مهمة / Important Notes

1. **التحديث كل دقيقة**: النظام يحدث البيانات كل 60 ثانية بالضبط
2. **ملفات منفصلة**: لا يتم خلط بيانات الأزواج مع سجل الصفقات
3. **الحفاظ على البيانات**: البيانات الأولية محفوظة مع كل تحديث
4. **الاتصال المستمر**: النظام يحافظ على الاتصال ويعيد الاتصال تلقائياً
5. **أمر واحد فقط**: كل شيء يعمل من أمر واحد `python app.py forex-system`

## 🎉 الخلاصة / Summary

النظام المتكامل الجديد يوفر:
- **نظام واحد متكامل** بدلاً من أوامر متعددة
- **ملفات منفصلة** لكل نوع من البيانات
- **تحديث مستمر كل دقيقة** لنسب الأرباح
- **فلترة ذكية** لأزواج العملات التقليدية فقط
- **اتصال مستقر** مع إعادة الاتصال التلقائي

استخدم الأمر: `python app.py forex-system` وستحصل على كل ما تحتاجه!
