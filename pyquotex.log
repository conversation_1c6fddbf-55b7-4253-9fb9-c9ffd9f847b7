2025-07-18 23:35:54,498 - __main__ - INFO - Quotex client initialized successfully.
2025-07-18 23:36:39,722 - __main__ - INFO - Quotex client initialized successfully.
2025-07-18 23:36:42,736 - __main__ - INFO - Establishing connection...
2025-07-18 23:36:42,737 - __main__ - INFO - Attempting to connect to Quotex API...
2025-07-18 23:37:44,851 - websocket - INFO - Websocket connected
2025-07-18 23:37:44,926 - pyquotex.ws.client - INFO - Websocket client connected.
2025-07-18 23:37:47,469 - __main__ - INFO - Connected successfully: Websocket connected successfully!!!
2025-07-18 23:37:47,469 - __main__ - INFO - Running connection test.
2025-07-18 23:37:49,480 - __main__ - INFO - Connection test successful.
2025-07-18 23:37:51,688 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-07-18 23:38:27,675 - __main__ - INFO - Quotex client initialized successfully.
2025-07-18 23:38:30,706 - __main__ - INFO - Establishing connection...
2025-07-18 23:38:30,706 - __main__ - INFO - Attempting to connect to Quotex API...
2025-07-18 23:38:31,947 - websocket - INFO - Websocket connected
2025-07-18 23:38:32,026 - pyquotex.ws.client - INFO - Websocket client connected.
2025-07-18 23:38:34,561 - __main__ - INFO - Connected successfully: Websocket connected successfully!!!
2025-07-18 23:38:34,562 - __main__ - INFO - Getting account balance.
2025-07-18 23:38:36,781 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-07-18 23:38:37,796 - __main__ - CRITICAL - Unexpected error occurred during command execution: object NoneType can't be used in 'await' expression
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\موقت بلوتوث\pyquotex-master\app.py", line 579, in main
    await cli.get_balance()
  File "C:\Users\<USER>\Downloads\موقت بلوتوث\pyquotex-master\app.py", line 95, in wrapper
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\موقت بلوتوث\pyquotex-master\app.py", line 191, in get_balance
    await self.client.change_account("PRACTICE")
TypeError: object NoneType can't be used in 'await' expression
2025-07-18 23:39:41,966 - __main__ - INFO - Quotex client initialized successfully.
2025-07-18 23:39:44,978 - __main__ - INFO - Establishing connection...
2025-07-18 23:39:44,980 - __main__ - INFO - Attempting to connect to Quotex API...
2025-07-18 23:39:46,375 - websocket - INFO - Websocket connected
2025-07-18 23:39:46,453 - pyquotex.ws.client - INFO - Websocket client connected.
2025-07-18 23:39:48,976 - __main__ - INFO - Connected successfully: Websocket connected successfully!!!
2025-07-18 23:39:48,979 - __main__ - INFO - Getting real-time price for EURUSD.
2025-07-18 23:39:48,981 - __main__ - ERROR - Asset EURUSD is closed or invalid for real-time monitoring.
2025-07-18 23:39:51,039 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-07-18 23:40:03,678 - __main__ - INFO - Quotex client initialized successfully.
2025-07-18 23:40:06,688 - __main__ - INFO - Establishing connection...
2025-07-18 23:40:06,690 - __main__ - INFO - Attempting to connect to Quotex API...
2025-07-18 23:40:08,734 - websocket - INFO - Websocket connected
2025-07-18 23:40:08,812 - pyquotex.ws.client - INFO - Websocket client connected.
2025-07-18 23:40:11,353 - __main__ - INFO - Connected successfully: Websocket connected successfully!!!
2025-07-18 23:40:11,353 - __main__ - INFO - Refilling practice account balance with R$ 5000.00.
2025-07-18 23:40:13,452 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-07-18 23:40:14,576 - __main__ - CRITICAL - Unexpected error occurred during command execution: object NoneType can't be used in 'await' expression
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\موقت بلوتوث\pyquotex-master\app.py", line 593, in main
    await cli.balance_refill(args.amount)
  File "C:\Users\<USER>\Downloads\موقت بلوتوث\pyquotex-master\app.py", line 95, in wrapper
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\موقت بلوتوث\pyquotex-master\app.py", line 404, in balance_refill
    await self.client.change_account("PRACTICE")
TypeError: object NoneType can't be used in 'await' expression
2025-07-18 23:40:26,989 - __main__ - INFO - Quotex client initialized successfully.
2025-07-18 23:40:30,006 - __main__ - INFO - Establishing connection...
2025-07-18 23:40:30,006 - __main__ - INFO - Attempting to connect to Quotex API...
2025-07-18 23:40:31,677 - websocket - INFO - Websocket connected
2025-07-18 23:40:31,754 - pyquotex.ws.client - INFO - Websocket client connected.
2025-07-18 23:40:34,801 - __main__ - INFO - Connected successfully: Websocket connected successfully!!!
2025-07-18 23:40:34,802 - __main__ - INFO - Getting candles for EURUSD with period of 60s.
2025-07-18 23:40:35,133 - __main__ - INFO - Retrieved 199 candles.
2025-07-18 23:40:37,331 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-07-21 23:04:13,407 - __main__ - INFO - Quotex client initialized successfully.
