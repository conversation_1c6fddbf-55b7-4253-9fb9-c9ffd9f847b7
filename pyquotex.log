2025-07-18 23:35:54,498 - __main__ - INFO - Quotex client initialized successfully.
2025-07-18 23:36:39,722 - __main__ - INFO - Quotex client initialized successfully.
2025-07-18 23:36:42,736 - __main__ - INFO - Establishing connection...
2025-07-18 23:36:42,737 - __main__ - INFO - Attempting to connect to Quotex API...
2025-07-18 23:37:44,851 - websocket - INFO - Websocket connected
2025-07-18 23:37:44,926 - pyquotex.ws.client - INFO - Websocket client connected.
2025-07-18 23:37:47,469 - __main__ - INFO - Connected successfully: Websocket connected successfully!!!
2025-07-18 23:37:47,469 - __main__ - INFO - Running connection test.
2025-07-18 23:37:49,480 - __main__ - INFO - Connection test successful.
2025-07-18 23:37:51,688 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-07-18 23:38:27,675 - __main__ - INFO - Quotex client initialized successfully.
2025-07-18 23:38:30,706 - __main__ - INFO - Establishing connection...
2025-07-18 23:38:30,706 - __main__ - INFO - Attempting to connect to Quotex API...
2025-07-18 23:38:31,947 - websocket - INFO - Websocket connected
2025-07-18 23:38:32,026 - pyquotex.ws.client - INFO - Websocket client connected.
2025-07-18 23:38:34,561 - __main__ - INFO - Connected successfully: Websocket connected successfully!!!
2025-07-18 23:38:34,562 - __main__ - INFO - Getting account balance.
2025-07-18 23:38:36,781 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-07-18 23:38:37,796 - __main__ - CRITICAL - Unexpected error occurred during command execution: object NoneType can't be used in 'await' expression
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\موقت بلوتوث\pyquotex-master\app.py", line 579, in main
    await cli.get_balance()
  File "C:\Users\<USER>\Downloads\موقت بلوتوث\pyquotex-master\app.py", line 95, in wrapper
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\موقت بلوتوث\pyquotex-master\app.py", line 191, in get_balance
    await self.client.change_account("PRACTICE")
TypeError: object NoneType can't be used in 'await' expression
2025-07-18 23:39:41,966 - __main__ - INFO - Quotex client initialized successfully.
2025-07-18 23:39:44,978 - __main__ - INFO - Establishing connection...
2025-07-18 23:39:44,980 - __main__ - INFO - Attempting to connect to Quotex API...
2025-07-18 23:39:46,375 - websocket - INFO - Websocket connected
2025-07-18 23:39:46,453 - pyquotex.ws.client - INFO - Websocket client connected.
2025-07-18 23:39:48,976 - __main__ - INFO - Connected successfully: Websocket connected successfully!!!
2025-07-18 23:39:48,979 - __main__ - INFO - Getting real-time price for EURUSD.
2025-07-18 23:39:48,981 - __main__ - ERROR - Asset EURUSD is closed or invalid for real-time monitoring.
2025-07-18 23:39:51,039 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-07-18 23:40:03,678 - __main__ - INFO - Quotex client initialized successfully.
2025-07-18 23:40:06,688 - __main__ - INFO - Establishing connection...
2025-07-18 23:40:06,690 - __main__ - INFO - Attempting to connect to Quotex API...
2025-07-18 23:40:08,734 - websocket - INFO - Websocket connected
2025-07-18 23:40:08,812 - pyquotex.ws.client - INFO - Websocket client connected.
2025-07-18 23:40:11,353 - __main__ - INFO - Connected successfully: Websocket connected successfully!!!
2025-07-18 23:40:11,353 - __main__ - INFO - Refilling practice account balance with R$ 5000.00.
2025-07-18 23:40:13,452 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-07-18 23:40:14,576 - __main__ - CRITICAL - Unexpected error occurred during command execution: object NoneType can't be used in 'await' expression
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\موقت بلوتوث\pyquotex-master\app.py", line 593, in main
    await cli.balance_refill(args.amount)
  File "C:\Users\<USER>\Downloads\موقت بلوتوث\pyquotex-master\app.py", line 95, in wrapper
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\موقت بلوتوث\pyquotex-master\app.py", line 404, in balance_refill
    await self.client.change_account("PRACTICE")
TypeError: object NoneType can't be used in 'await' expression
2025-07-18 23:40:26,989 - __main__ - INFO - Quotex client initialized successfully.
2025-07-18 23:40:30,006 - __main__ - INFO - Establishing connection...
2025-07-18 23:40:30,006 - __main__ - INFO - Attempting to connect to Quotex API...
2025-07-18 23:40:31,677 - websocket - INFO - Websocket connected
2025-07-18 23:40:31,754 - pyquotex.ws.client - INFO - Websocket client connected.
2025-07-18 23:40:34,801 - __main__ - INFO - Connected successfully: Websocket connected successfully!!!
2025-07-18 23:40:34,802 - __main__ - INFO - Getting candles for EURUSD with period of 60s.
2025-07-18 23:40:35,133 - __main__ - INFO - Retrieved 199 candles.
2025-07-18 23:40:37,331 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-07-21 23:04:13,407 - __main__ - INFO - Quotex client initialized successfully.
2025-07-21 23:28:10,943 - __main__ - INFO - Quotex client initialized successfully.
2025-07-21 23:28:11,962 - __main__ - INFO - Starting continuous monitoring with persistent connection.
2025-07-21 23:28:13,975 - __main__ - INFO - Establishing initial connection...
2025-07-21 23:28:13,975 - __main__ - INFO - Attempting to connect to Quotex API...
2025-07-21 23:28:15,688 - websocket - INFO - Websocket connected
2025-07-21 23:28:15,764 - pyquotex.ws.client - INFO - Websocket client connected.
2025-07-21 23:28:16,981 - pyquotex.ws.client - INFO - Disconnection event triggered by the platform, causing automatic reconnection.
2025-07-21 23:28:16,982 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-07-21 23:28:20,614 - websocket - INFO - Websocket connected
2025-07-21 23:28:20,615 - pyquotex.ws.client - INFO - Websocket client connected.
2025-07-21 23:29:08,627 - websocket - DEBUG - Sending ping
2025-07-21 23:29:32,640 - websocket - DEBUG - Sending ping
2025-07-21 23:29:39,576 - __main__ - INFO - Connected successfully: Websocket Token Rejected.
2025-07-21 23:29:39,577 - __main__ - INFO - Starting persistent connection to Quotex platform.
2025-07-21 23:29:39,577 - __main__ - INFO - Starting continuous asset monitoring.
2025-07-21 23:29:39,636 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:40,683 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:41,754 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:42,802 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:43,848 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:44,885 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:45,976 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:47,006 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:48,053 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:49,124 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:50,164 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:51,202 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:52,237 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:53,278 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:54,321 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:55,361 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:56,440 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:56,645 - websocket - DEBUG - Sending ping
2025-07-21 23:29:57,473 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:58,524 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:29:59,566 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:30:00,609 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:30:01,658 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:30:02,700 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:30:03,735 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:30:04,784 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:30:05,827 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:30:06,874 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:30:07,923 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:30:08,972 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:30:10,011 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:30:11,059 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:30:12,109 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:30:13,159 - __main__ - ERROR - Error updating asset data: string index out of range
2025-07-21 23:30:20,646 - websocket - DEBUG - Sending ping
2025-07-21 23:30:44,658 - websocket - DEBUG - Sending ping
2025-07-21 23:31:08,663 - websocket - DEBUG - Sending ping
2025-07-21 23:31:32,675 - websocket - DEBUG - Sending ping
2025-07-21 23:31:56,679 - websocket - DEBUG - Sending ping
2025-07-21 23:32:20,689 - websocket - DEBUG - Sending ping
2025-07-21 23:32:44,700 - websocket - DEBUG - Sending ping
2025-07-21 23:33:08,702 - websocket - DEBUG - Sending ping
2025-07-21 23:33:32,717 - websocket - DEBUG - Sending ping
2025-07-21 23:33:43,051 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-07-21 23:33:44,052 - __main__ - INFO - Connection closed.
2025-07-21 23:38:47,249 - __main__ - INFO - Quotex client initialized successfully.
2025-07-21 23:38:48,300 - __main__ - INFO - Starting forex monitoring with JSON saving and persistent connection.
2025-07-21 23:38:50,309 - __main__ - INFO - Establishing initial connection...
2025-07-21 23:38:50,309 - __main__ - INFO - Attempting to connect to Quotex API...
2025-07-21 23:38:52,576 - websocket - INFO - Websocket connected
2025-07-21 23:38:52,654 - pyquotex.ws.client - INFO - Websocket client connected.
2025-07-21 23:38:56,189 - __main__ - INFO - Connected successfully: Websocket connected successfully!!!
2025-07-21 23:38:56,193 - __main__ - CRITICAL - Unexpected error occurred during command execution: object NoneType can't be used in 'await' expression
Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\موقت بلوتوث\pyquotex-master\app.py", line 1311, in main
    await cli.start_forex_monitoring_with_json(args.save_interval)
  File "C:\Users\<USER>\Downloads\موقت بلوتوث\pyquotex-master\app.py", line 1155, in start_forex_monitoring_with_json
    await self.client.change_account("PRACTICE")
TypeError: object NoneType can't be used in 'await' expression
2025-07-21 23:42:23,019 - __main__ - INFO - Quotex client initialized successfully.
2025-07-21 23:42:24,039 - __main__ - INFO - Starting forex monitoring with JSON saving and persistent connection.
2025-07-21 23:42:26,047 - __main__ - INFO - Establishing initial connection...
2025-07-21 23:42:26,048 - __main__ - INFO - Attempting to connect to Quotex API...
2025-07-21 23:42:28,003 - websocket - INFO - Websocket connected
2025-07-21 23:42:28,081 - pyquotex.ws.client - INFO - Websocket client connected.
2025-07-21 23:42:32,113 - __main__ - INFO - Connected successfully: Websocket connected successfully!!!
2025-07-21 23:42:32,114 - __main__ - INFO - Starting persistent connection to Quotex platform.
2025-07-21 23:42:32,115 - __main__ - INFO - Starting forex monitoring with JSON saving.
2025-07-21 23:42:35,325 - __main__ - ERROR - Error collecting data: string index out of range
2025-07-21 23:42:35,330 - __main__ - INFO - Data saved to data/forex_monitoring/forex_data_20250721_234235.json
2025-07-21 23:43:07,829 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-07-21 23:43:09,801 - __main__ - INFO - Connection closed.
2025-07-22 13:50:53,423 - __main__ - INFO - Quotex client initialized successfully.
2025-07-22 13:50:54,443 - __main__ - INFO - Starting continuous monitoring with persistent connection.
2025-07-22 13:50:56,452 - __main__ - INFO - Establishing initial connection...
2025-07-22 13:50:56,452 - __main__ - INFO - Attempting to connect to Quotex API...
2025-07-22 13:50:58,980 - websocket - INFO - Websocket connected
2025-07-22 13:50:59,058 - pyquotex.ws.client - INFO - Websocket client connected.
2025-07-22 13:50:59,684 - pyquotex.ws.client - INFO - Disconnection event triggered by the platform, causing automatic reconnection.
2025-07-22 13:50:59,685 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-07-22 13:51:03,448 - websocket - INFO - Websocket connected
2025-07-22 13:51:03,449 - pyquotex.ws.client - INFO - Websocket client connected.
2025-07-22 13:51:48,080 - __main__ - INFO - Connected successfully: Websocket Token Rejected.
2025-07-22 13:51:48,082 - __main__ - INFO - Starting persistent connection to Quotex platform.
2025-07-22 13:51:48,082 - __main__ - INFO - Starting continuous asset monitoring.
2025-07-22 13:51:51,466 - websocket - DEBUG - Sending ping
2025-07-22 13:52:15,470 - websocket - DEBUG - Sending ping
2025-07-22 13:52:39,480 - websocket - DEBUG - Sending ping
2025-07-22 13:53:03,485 - websocket - DEBUG - Sending ping
2025-07-22 13:53:27,486 - websocket - DEBUG - Sending ping
2025-07-22 13:53:51,493 - websocket - DEBUG - Sending ping
2025-07-22 13:54:15,495 - websocket - DEBUG - Sending ping
2025-07-22 13:54:39,505 - websocket - DEBUG - Sending ping
2025-07-22 13:55:03,521 - websocket - DEBUG - Sending ping
2025-07-22 13:55:14,242 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-07-22 13:55:15,250 - __main__ - INFO - Connection closed.
2025-07-22 13:55:20,040 - __main__ - INFO - Quotex client initialized successfully.
2025-07-22 13:55:21,059 - __main__ - INFO - Starting continuous monitoring with persistent connection.
2025-07-22 13:55:23,073 - __main__ - INFO - Establishing initial connection...
2025-07-22 13:55:23,075 - __main__ - INFO - Attempting to connect to Quotex API...
2025-07-22 13:55:25,755 - websocket - INFO - Websocket connected
2025-07-22 13:55:25,834 - pyquotex.ws.client - INFO - Websocket client connected.
2025-07-22 13:55:28,377 - __main__ - INFO - Connected successfully: Websocket connected successfully!!!
2025-07-22 13:55:28,379 - __main__ - INFO - Starting persistent connection to Quotex platform.
2025-07-22 13:55:28,379 - __main__ - INFO - Starting continuous asset monitoring.
2025-07-22 13:55:32,585 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-07-22 13:55:33,802 - __main__ - INFO - Connection closed.
