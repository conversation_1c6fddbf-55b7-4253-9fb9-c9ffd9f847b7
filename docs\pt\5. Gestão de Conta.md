# Gestão de Conta no PyQuotex

Esta documentação detalha as funcionalidades relacionadas à gestão de conta na API do PyQuotex.

## Configuração Inicial

```python
from pyquotex.stable_api import Quotex

# Inicializar o cliente
client = Quotex(
    email="<EMAIL>",
    password="suasenha",
    lang="pt"  # Idioma padrão: português (pt)
)

# Conectar com a API
await client.connect()
```

## Obtenção do Perfil

Para obter as informações do perfil do usuário:

```python
async def obter_perfil():
    profile = await client.get_profile()
    
    # Informações disponíveis
    print(f"Usuário: {profile.nick_name}")
    print(f"Saldo Demo: {profile.demo_balance}")
    print(f"Saldo Real: {profile.live_balance}")
    print(f"ID: {profile.profile_id}")
    print(f"Avatar: {profile.avatar}")
    print(f"País: {profile.country_name}")
    print(f"Fu<PERSON>: {profile.offset}")
```

## Consulta de Saldo

Para consultar o saldo atual da conta:

```python
async def consultar_saldo():
    saldo = await client.get_balance()
    print(f"Saldo Atual: {saldo}")
```

O saldo mostrado corresponderá à conta ativa (demo ou real).

## Recarga de Saldo Demo

Para recarregar o saldo na conta demo:

```python
async def recarregar_saldo_demo():
    # Recarregar 5000 na conta demo
    resultado = await client.edit_practice_balance(5000)
    print(resultado)
```

## Histórico de Operações

Para obter o histórico de operações:

```python
async def obter_historico():
    # Obtém o histórico da conta ativa
    historico = await client.get_history()
    
    for operacao in historico:
        print(f"ID: {operacao.get('ticket')}")
        print(f"Lucro: {operacao.get('profitAmount')}")
        # Outros dados disponíveis no histórico
```

Também pode consultar o resultado de uma operação específica:

```python
async def consultar_operacao(operation_id):
    status, detalhes = await client.get_result(operation_id)
    # status pode ser "win" ou "loss"
    print(f"Resultado: {status}")
    print(f"Detalhes: {detalhes}")
```

## Alternar entre Contas Demo/Real

Para alternar entre conta demo e real:

```python
# Mudar para conta real
client.set_account_mode("REAL")

# Mudar para conta demo
client.set_account_mode("PRACTICE")

# Também pode usar o método alternativo
client.change_account("REAL")  # ou "PRACTICE"
```

### Notas Importantes:

1. A conta demo é o modo padrão ao inicializar o cliente.
2. Certifique-se de ter uma conexão estabelecida antes de realizar operações.
3. O saldo demo pode ser recarregado, mas o saldo real não.
4. As operações de conta real envolvem dinheiro real, use-as com cautela.
5. Sempre verifique o modo de conta ativo antes de realizar operações.

## Tratamento de Erros

É recomendável implementar tratamento de erros nas operações:

```python
async def operacao_segura():
    try:
        check_connect, message = await client.connect()
        if check_connect:
            # Realizar operações
            pass
        else:
            print(f"Erro de conexão: {message}")
    except Exception as e:
        print(f"Erro: {str(e)}")
    finally:
        await cliente.close()
```