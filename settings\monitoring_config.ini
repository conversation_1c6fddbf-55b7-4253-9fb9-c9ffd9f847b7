[MONITORING]
# فترة التحديث بالثواني / Update interval in seconds
update_interval = 30

# عدد الأزواج المعروضة / Number of assets to display
max_assets_display = 50

# تمكين الألوان / Enable colors
enable_colors = true

# مسح الشاشة عند التحديث / Clear screen on update
clear_screen = true

# عرض الإحصائيات / Show statistics
show_statistics = true

# عرض أفضل الأزواج / Show best assets
show_best_assets = true

[CONNECTION]
# عدد محاولات إعادة الاتصال / Number of reconnection attempts
max_reconnect_attempts = 10

# فترة التحقق من الاتصال بالثواني / Connection check interval in seconds
connection_check_interval = 5

# فترة الانتظار بين محاولات الاتصال بالثواني / Wait time between connection attempts in seconds
reconnect_wait_time = 10

# مهلة الاتصال بالثواني / Connection timeout in seconds
connection_timeout = 30

[DISPLAY]
# عرض مؤشرات الاتجاه / Show trend indicators
show_trend_indicators = true

# عرض النسب المئوية للأرباح / Show profit percentages
show_profit_percentages = true

# الحد الأدنى للربح العالي / High profit threshold
high_profit_threshold = 80

# تنسيق الوقت / Time format
time_format = %Y-%m-%d %H:%M:%S

# عرض الأزواج المغلقة / Show closed assets
show_closed_assets = true

# ترتيب الأزواج / Asset sorting (profit_desc, profit_asc, name_asc, name_desc, status)
asset_sorting = profit_desc

[ALERTS]
# تمكين التنبيهات / Enable alerts
enable_alerts = false

# تنبيه عند ربح عالي / Alert on high profit
alert_high_profit = true

# تنبيه عند فتح زوج جديد / Alert on new asset open
alert_new_asset_open = false

# تنبيه عند إغلاق زوج / Alert on asset close
alert_asset_close = false

# الحد الأدنى للربح للتنبيه / Minimum profit for alert
alert_profit_threshold = 85

[LOGGING]
# مستوى السجل / Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
log_level = INFO

# حفظ السجلات في ملف / Save logs to file
save_logs_to_file = true

# مسار ملف السجل / Log file path
log_file_path = logs/continuous_monitoring.log

# حجم ملف السجل الأقصى بالميجابايت / Maximum log file size in MB
max_log_file_size = 10

# عدد ملفات السجل المحفوظة / Number of backup log files
log_backup_count = 5

[PERFORMANCE]
# تمكين التحسينات / Enable optimizations
enable_optimizations = true

# استخدام ذاكرة التخزين المؤقت / Use caching
use_caching = true

# مدة صلاحية ذاكرة التخزين المؤقت بالثواني / Cache expiry time in seconds
cache_expiry_time = 60

# الحد الأقصى لاستخدام الذاكرة بالميجابايت / Maximum memory usage in MB
max_memory_usage = 512

[FILTERS]
# تصفية الأزواج حسب النوع / Filter assets by type
filter_by_type = false

# أنواع الأزواج المسموحة / Allowed asset types (forex, crypto, commodities, indices)
allowed_asset_types = forex,crypto,commodities,indices

# تصفية الأزواج حسب الربح الأدنى / Filter by minimum profit
filter_by_min_profit = false

# الحد الأدنى للربح للتصفية / Minimum profit for filtering
min_profit_filter = 60

# تصفية الأزواج المغلقة / Filter closed assets
filter_closed_assets = false

# قائمة الأزواج المفضلة / Favorite assets list
favorite_assets = EURUSD_otc,GBPUSD_otc,USDJPY_otc,AUDUSD_otc

# عرض الأزواج المفضلة فقط / Show only favorite assets
show_only_favorites = false

[EXPORT]
# تمكين التصدير / Enable export
enable_export = false

# تنسيق التصدير / Export format (csv, json, xlsx)
export_format = csv

# مسار ملف التصدير / Export file path
export_file_path = data/monitoring_export

# تصدير تلقائي / Auto export
auto_export = false

# فترة التصدير التلقائي بالدقائق / Auto export interval in minutes
auto_export_interval = 60

[NOTIFICATIONS]
# تمكين الإشعارات / Enable notifications
enable_notifications = false

# نوع الإشعار / Notification type (console, email, webhook)
notification_type = console

# عنوان البريد الإلكتروني للإشعارات / Email for notifications
notification_email = 

# رابط webhook للإشعارات / Webhook URL for notifications
webhook_url = 

# إشعار عند بدء المراقبة / Notify on monitoring start
notify_on_start = true

# إشعار عند إيقاف المراقبة / Notify on monitoring stop
notify_on_stop = true

# إشعار عند أخطاء الاتصال / Notify on connection errors
notify_on_connection_error = true
